import React, { useState, useEffect, useRef } from 'react';
import { AlertCircle, Target, Database, Zap, CheckCircle, Shield, Save, ChevronLeft, ChevronRight, ArrowRight, Loader2 } from 'lucide-react';
import { useAnalysis } from '../../context/AnalysisContext';
import { api } from '../../api/apiClient';
import ctiService from '../../services/ctiService';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../utils/toastUtils';

// Clean, simple components
const Section = ({ icon, title, children, step }) => (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 sm:p-8 transition-all duration-500 ease-in-out">
        <div className="flex items-center mb-6">
            <div className="flex items-center justify-center bg-indigo-100 text-indigo-600 rounded-full h-12 w-12 mr-4">
                {icon}
            </div>
            <div>
                <p className="text-sm font-semibold text-indigo-600">ÉTAPE {step}</p>
                <h2 className="text-xl font-bold text-gray-800">{title}</h2>
            </div>
        </div>
        {children}
    </div>
);

// Attack Path Carousel (the part you like)
const AttackPathCarousel = ({ attackPaths, selectedPath, onSelect, isLoading }) => {
    const scrollRef = useRef(null);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(true);

    useEffect(() => {
        const handleScroll = () => updateScrollState();
        const handleResize = () => updateScrollState();

        if (scrollRef.current) {
            scrollRef.current.addEventListener('scroll', handleScroll);
            window.addEventListener('resize', handleResize);
            updateScrollState();
        }

        return () => {
            if (scrollRef.current) {
                scrollRef.current.removeEventListener('scroll', handleScroll);
            }
            window.removeEventListener('resize', handleResize);
        };
    }, [attackPaths]);

    if (isLoading) return <div className="text-center py-8"><Loader2 className="h-8 w-8 animate-spin mx-auto text-indigo-500" /></div>;
    if (attackPaths.length === 0) return <div className="text-center py-8 text-gray-500">Aucun chemin d'attaque trouvé.</div>;

    const getItemsPerView = () => {
        if (typeof window !== 'undefined') {
            if (window.innerWidth >= 1024) return 3;
            if (window.innerWidth >= 768) return 2;
            return 1;
        }
        return 3;
    };

    const itemsPerView = getItemsPerView();
    const maxIndex = Math.max(0, attackPaths.length - itemsPerView);

    const updateScrollState = () => {
        if (scrollRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
            setCanScrollLeft(scrollLeft > 0);
            setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10);
        }
    };

    const scrollLeft = () => {
        if (scrollRef.current && canScrollLeft) {
            const newIndex = Math.max(0, currentIndex - 1);
            setCurrentIndex(newIndex);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24;
            scrollRef.current.scrollTo({
                left: newIndex * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    const scrollRight = () => {
        if (scrollRef.current && canScrollRight) {
            const newIndex = Math.min(maxIndex, currentIndex + 1);
            setCurrentIndex(newIndex);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24;
            scrollRef.current.scrollTo({
                left: newIndex * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    const goToIndex = (index) => {
        if (scrollRef.current) {
            setCurrentIndex(index);
            const cardWidth = scrollRef.current.children[0]?.offsetWidth || 0;
            const gap = 24;
            scrollRef.current.scrollTo({
                left: index * (cardWidth + gap),
                behavior: 'smooth'
            });
        }
    };

    return (
        <div className="relative">
            {attackPaths.length > itemsPerView && (
                <>
                    <button
                        onClick={scrollLeft}
                        disabled={!canScrollLeft}
                        className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 ${
                            canScrollLeft
                                ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-indigo-600'
                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                    >
                        <ChevronLeft className="h-5 w-5" />
                    </button>
                    <button
                        onClick={scrollRight}
                        disabled={!canScrollRight}
                        className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 ${
                            canScrollRight
                                ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-indigo-600'
                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        }`}
                    >
                        <ChevronRight className="h-5 w-5" />
                    </button>
                </>
            )}

            <div
                ref={scrollRef}
                className="flex overflow-x-auto snap-x snap-mandatory scroll-smooth py-4 px-12 space-x-4"
                style={{ scrollbarWidth: 'none', '-ms-overflow-style': 'none' }}
            >
                {attackPaths.map(path => (
                    <div key={path.id} className="flex-shrink-0 w-full sm:w-2/3 md:w-1/2 lg:w-1/3 snap-start">
                        <div onClick={() => onSelect(path)} className={`group relative h-full flex flex-col rounded-2xl p-5 cursor-pointer border transition-all duration-300 transform hover:scale-105 ${selectedPath?.id === path.id ? 'border-indigo-400 bg-gradient-to-br from-indigo-50 to-blue-50 shadow-xl shadow-indigo-100' : 'border-gray-200 bg-white hover:border-indigo-200 hover:shadow-lg'}`}>
                            {selectedPath?.id === path.id && (
                                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-400 to-blue-400 opacity-20 blur-sm"></div>
                            )}

                            <div className="relative flex justify-between items-start mb-3">
                                <div className="flex items-center space-x-2">
                                    <span className="text-xs font-bold text-white bg-gradient-to-r from-indigo-500 to-blue-500 px-2.5 py-1 rounded-lg shadow-sm">
                                        {path.referenceCode || `CA${attackPaths.indexOf(path) + 1}`}
                                    </span>
                                    <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                                </div>
                                {selectedPath?.id === path.id && (
                                    <div className="bg-indigo-500 rounded-full p-1">
                                        <CheckCircle className="h-4 w-4 text-white" />
                                    </div>
                                )}
                            </div>

                            <div className="relative flex-grow">
                                <h3 className="text-base font-bold text-gray-800 mb-3 leading-tight line-clamp-2 group-hover:text-indigo-700 transition-colors">
                                    {path.objectifVise}
                                </h3>

                                <div className="space-y-2.5">
                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-red-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Source</p>
                                            <p className="text-sm text-gray-700 font-medium truncate">{path.sourceRiskName}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-orange-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Événement</p>
                                            <p className="text-sm text-gray-700 truncate">{path.dreadedEventName}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-start space-x-2">
                                        <div className="w-2 h-2 rounded-full bg-blue-400 mt-1.5 flex-shrink-0"></div>
                                        <div className="min-w-0 flex-1">
                                            <p className="text-xs text-gray-500 font-medium">Valeur</p>
                                            <p className="text-sm text-gray-700 truncate">{path.businessValueName}</p>
                                        </div>
                                    </div>
                                </div>

                                {path.stakeholders && path.stakeholders.length > 0 && (
                                    <div className="mt-3 flex flex-wrap gap-1">
                                        {path.stakeholders.slice(0, 2).map((stakeholder, index) => (
                                            <span key={stakeholder.id || index} className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                                                {stakeholder.name.length > 8 ? stakeholder.name.substring(0, 8) + '...' : stakeholder.name}
                                            </span>
                                        ))}
                                        {path.stakeholders.length > 2 && (
                                            <span className="text-xs bg-indigo-100 text-indigo-600 px-2 py-0.5 rounded-full font-medium">
                                                +{path.stakeholders.length - 2}
                                            </span>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="relative mt-4 pt-3 border-t border-gray-100">
                                <div className="flex items-center justify-between">
                                    <span className="text-xs text-gray-500 font-medium">
                                        {selectedPath?.id === path.id ? '✓ Sélectionné' : 'Cliquer pour sélectionner'}
                                    </span>
                                    <ArrowRight className={`h-3.5 w-3.5 transition-all duration-200 ${selectedPath?.id === path.id ? 'text-indigo-500 transform translate-x-1' : 'text-gray-400 group-hover:text-indigo-400 group-hover:transform group-hover:translate-x-1'}`} />
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {attackPaths.length > itemsPerView && (
                <div className="flex justify-center mt-4 space-x-2">
                    {Array.from({ length: maxIndex + 1 }, (_, index) => (
                        <button
                            key={index}
                            onClick={() => goToIndex(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-200 ${
                                index === currentIndex
                                    ? 'bg-indigo-500 w-6'
                                    : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

// Business Assets Cards with CTI Results
const BusinessAssetsCards = ({ assets, onAnalyze, isLoading, analysisType }) => {
    if (!assets || assets.length === 0) return (
        <div className="text-center py-12">
            <Database className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">Aucun bien support pour ce chemin d'attaque</p>
        </div>
    );

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {assets.map(asset => (
                <div key={asset.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                    {/* Asset Header */}
                    <div className="mb-4">
                        <h3 className="text-lg font-semibold text-gray-800">{asset.name}</h3>
                        <p className="text-sm text-gray-500">{asset.type}</p>
                        {asset.vendor && <p className="text-xs text-gray-400">{asset.vendor}</p>}
                    </div>

                    {/* CTI Analysis Buttons */}
                    <div className="mb-4 space-y-2">
                        <button
                            onClick={() => onAnalyze('nist', asset)}
                            disabled={isLoading}
                            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 transition-colors"
                        >
                            {isLoading && analysisType === 'nist' && asset.isAnalyzing ?
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
                                <Shield className="mr-2 h-4 w-4" />
                            }
                            {isLoading && analysisType === 'nist' && asset.isAnalyzing ? 'Analyse...' : 'NIST'}
                        </button>

                        <button
                            onClick={() => onAnalyze('mitre', asset)}
                            disabled={isLoading}
                            className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 transition-colors"
                        >
                            {isLoading && analysisType === 'mitre' && asset.isAnalyzing ?
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
                                <Target className="mr-2 h-4 w-4" />
                            }
                            {isLoading && analysisType === 'mitre' && asset.isAnalyzing ? 'Analyse...' : 'MITRE'}
                        </button>
                    </div>

                    {/* CTI Results */}
                    {asset.ctiResults && (
                        <div className="border-t pt-4">
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Résultats CTI</h4>
                            <div className="space-y-2">
                                {asset.ctiResults.vulnerabilities && asset.ctiResults.vulnerabilities.length > 0 && (
                                    <div className="text-sm">
                                        <span className="font-medium text-blue-600">{asset.ctiResults.vulnerabilities.length}</span>
                                        <span className="text-gray-600"> vulnérabilités</span>
                                    </div>
                                )}
                                {asset.ctiResults.attackTechniques && asset.ctiResults.attackTechniques.length > 0 && (
                                    <div className="text-sm">
                                        <span className="font-medium text-red-600">{asset.ctiResults.attackTechniques.length}</span>
                                        <span className="text-gray-600"> techniques</span>
                                    </div>
                                )}
                                {asset.ctiResults.dataSource && (
                                    <div className="text-xs text-gray-500">
                                        Source: {asset.ctiResults.dataSource}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* No Results Message */}
                    {!asset.ctiResults && (
                        <div className="border-t pt-4 text-center">
                            <p className="text-sm text-gray-500">Aucune analyse effectuée</p>
                        </div>
                    )}
                </div>
            ))}
        </div>
    );
};

// Results Summary Section
const ResultsSummary = ({ assets, onSave, isSaving }) => {
    // Filter assets that have CTI results
    const analyzedAssets = assets.filter(asset => asset.ctiResults);

    if (analyzedAssets.length === 0) {
        return (
            <div className="text-center py-12">
                <Zap className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">Aucune analyse CTI effectuée</p>
                <p className="text-sm text-gray-400">Sélectionnez un chemin d'attaque et lancez une analyse pour voir les résultats</p>
            </div>
        );
    }

    // Calculate totals
    const totalVulnerabilities = analyzedAssets.reduce((sum, asset) =>
        sum + (asset.ctiResults?.vulnerabilities?.length || 0), 0
    );
    const totalTechniques = analyzedAssets.reduce((sum, asset) =>
        sum + (asset.ctiResults?.attackTechniques?.length || 0), 0
    );

    return (
        <div className="space-y-6">
            {/* Save Button */}
            <div className="flex justify-end">
                <button
                    onClick={onSave}
                    disabled={isSaving}
                    className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium shadow-md hover:shadow-lg"
                >
                    {isSaving ? (
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    ) : (
                        <Save className="mr-2 h-5 w-5" />
                    )}
                    {isSaving ? 'Sauvegarde...' : 'Sauvegarder les résultats'}
                </button>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">{totalVulnerabilities}</div>
                    <div className="text-sm font-medium text-blue-800">Vulnérabilités totales</div>
                </div>
                <div className="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-red-600 mb-2">{totalTechniques}</div>
                    <div className="text-sm font-medium text-red-800">Techniques d'attaque</div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6 text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">{analyzedAssets.length}</div>
                    <div className="text-sm font-medium text-green-800">Actifs analysés</div>
                </div>
            </div>

            {/* Detailed Results by Asset */}
            <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">Détails par actif</h3>
                <div className="space-y-4">
                    {analyzedAssets.map(asset => (
                        <div key={asset.id} className="bg-white border border-gray-200 rounded-lg p-6">
                            {/* Asset Header */}
                            <div className="flex items-center justify-between mb-4">
                                <div>
                                    <h4 className="text-lg font-semibold text-gray-800">{asset.name}</h4>
                                    <p className="text-sm text-gray-500">{asset.type}</p>
                                    {asset.vendor && <p className="text-xs text-gray-400">{asset.vendor}</p>}
                                </div>
                                <div className="text-right">
                                    <div className="text-sm text-gray-500">Source: {asset.ctiResults?.dataSource}</div>
                                    <div className="text-xs text-gray-400">
                                        {asset.ctiResults?.analysisDate && new Date(asset.ctiResults.analysisDate).toLocaleString('fr-FR')}
                                    </div>
                                </div>
                            </div>

                            {/* Results Grid */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* Vulnerabilities */}
                                {asset.ctiResults?.vulnerabilities && asset.ctiResults.vulnerabilities.length > 0 && (
                                    <div>
                                        <h5 className="text-md font-medium text-blue-700 mb-3 flex items-center">
                                            <Shield className="h-4 w-4 mr-2" />
                                            Vulnérabilités ({asset.ctiResults.vulnerabilities.length})
                                        </h5>
                                        <div className="space-y-2 max-h-48 overflow-y-auto">
                                            {asset.ctiResults.vulnerabilities.slice(0, 5).map((vuln, index) => (
                                                <div key={vuln.cveId || vuln.id || index} className="bg-blue-50 rounded-lg p-3">
                                                    <div className="font-medium text-sm text-blue-800">
                                                        {vuln.cveId || vuln.id || `VULN-${index + 1}`}
                                                    </div>
                                                    {vuln.description && (
                                                        <div className="text-xs text-blue-600 mt-1 line-clamp-2">
                                                            {vuln.description.length > 100
                                                                ? vuln.description.substring(0, 100) + '...'
                                                                : vuln.description
                                                            }
                                                        </div>
                                                    )}
                                                    {vuln.severity && (
                                                        <div className="text-xs mt-1">
                                                            <span className={`px-2 py-1 rounded-full ${
                                                                vuln.severity.toLowerCase() === 'critical' ? 'bg-red-100 text-red-700' :
                                                                vuln.severity.toLowerCase() === 'high' ? 'bg-orange-100 text-orange-700' :
                                                                vuln.severity.toLowerCase() === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                                                                'bg-gray-100 text-gray-700'
                                                            }`}>
                                                                {vuln.severity}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                            {asset.ctiResults.vulnerabilities.length > 5 && (
                                                <div className="text-center text-sm text-blue-600 font-medium">
                                                    +{asset.ctiResults.vulnerabilities.length - 5} autres vulnérabilités
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}

                                {/* Attack Techniques */}
                                {asset.ctiResults?.attackTechniques && asset.ctiResults.attackTechniques.length > 0 && (
                                    <div>
                                        <h5 className="text-md font-medium text-red-700 mb-3 flex items-center">
                                            <Target className="h-4 w-4 mr-2" />
                                            Techniques d'attaque ({asset.ctiResults.attackTechniques.length})
                                        </h5>
                                        <div className="space-y-2 max-h-48 overflow-y-auto">
                                            {asset.ctiResults.attackTechniques.slice(0, 5).map((technique, index) => (
                                                <div key={technique.id || technique.techniqueId || index} className="bg-red-50 rounded-lg p-3">
                                                    <div className="font-medium text-sm text-red-800">
                                                        {technique.id || technique.techniqueId || `T${index + 1000}`}
                                                    </div>
                                                    <div className="text-sm text-red-700 font-medium">
                                                        {technique.name || technique.technique_name || 'Technique inconnue'}
                                                    </div>
                                                    {technique.description && (
                                                        <div className="text-xs text-red-600 mt-1 line-clamp-2">
                                                            {technique.description.length > 100
                                                                ? technique.description.substring(0, 100) + '...'
                                                                : technique.description
                                                            }
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                            {asset.ctiResults.attackTechniques.length > 5 && (
                                                <div className="text-center text-sm text-red-600 font-medium">
                                                    +{asset.ctiResults.attackTechniques.length - 5} autres techniques
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* No Results Message */}
                            {(!asset.ctiResults?.vulnerabilities || asset.ctiResults.vulnerabilities.length === 0) &&
                             (!asset.ctiResults?.attackTechniques || asset.ctiResults.attackTechniques.length === 0) && (
                                <div className="text-center py-8 text-gray-500">
                                    <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                                    <p>Aucun résultat trouvé pour cet actif</p>
                                </div>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};



// Main Component - Super Simple
const ThreatIntelligenceModern = () => {
    const { currentAnalysis } = useAnalysis();

    // Simple state management
    const [isLoading, setIsLoading] = useState({ paths: false, assets: false, analysis: false });
    const [error, setError] = useState(null);
    const [attackPaths, setAttackPaths] = useState([]);
    const [selectedPath, setSelectedPath] = useState(null);
    const [businessAssets, setBusinessAssets] = useState([]);
    const [currentAnalysisType, setCurrentAnalysisType] = useState(null);
    const [analyzingAssetId, setAnalyzingAssetId] = useState(null);
    const [isSaving, setIsSaving] = useState(false);

    // Load attack paths
    useEffect(() => {
        if (!currentAnalysis) return;
        setIsLoading(s => ({ ...s, paths: true }));
        api.get(`/analyses/${currentAnalysis.id}/attack-paths`)
            .then(res => {
                let paths = [];
                if (res.data?.attackPaths) {
                    paths = res.data.attackPaths;
                } else if (res.data?.data?.attackPaths) {
                    paths = res.data.data.attackPaths;
                } else if (Array.isArray(res.data)) {
                    paths = res.data;
                }
                setAttackPaths(paths);
            })
            .catch((error) => {
                console.error('Error loading attack paths:', error);
                setError("Erreur de chargement des chemins d'attaque.");
            })
            .finally(() => setIsLoading(s => ({ ...s, paths: false })));
    }, [currentAnalysis]);

    // Load business assets when path is selected
    useEffect(() => {
        if (!selectedPath) {
            setBusinessAssets([]);
            return;
        }
        setIsLoading(s => ({ ...s, assets: true }));
        api.get(`/analyses/${currentAnalysis.id}/business-values`)
            .then(res => {
                const bvs = res.data?.data?.businessValues || [];
                const targetBv = bvs.find(bv => bv.id === selectedPath.businessValueId);
                const assets = targetBv?.supportAssets || [];
                // Initialize each asset with empty CTI results
                setBusinessAssets(assets.map(asset => ({
                    ...asset,
                    ctiResults: null,
                    isAnalyzing: false
                })));
            })
            .catch((error) => {
                console.error('Error loading support assets:', error);
                setError("Erreur de chargement des biens supports.");
            })
            .finally(() => setIsLoading(s => ({ ...s, assets: false })));
    }, [selectedPath, currentAnalysis?.id]);

    if (!currentAnalysis) {
        return (
            <div className="bg-gray-50 min-h-screen p-4 sm:p-6 lg:p-8">
                <div className="max-w-6xl mx-auto">
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <h2 className="text-xl font-semibold text-gray-900 mb-2">
                                Aucune analyse sélectionnée
                            </h2>
                            <p className="text-gray-600">
                                Veuillez sélectionner une analyse pour commencer l'intelligence des menaces.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Handle CTI analysis for a specific asset
    const handleAnalysis = async (analysisType, asset) => {
        // Mark this asset as being analyzed
        setBusinessAssets(assets => assets.map(a =>
            a.id === asset.id ? { ...a, isAnalyzing: true } : a
        ));

        setIsLoading(s => ({ ...s, analysis: true }));
        setCurrentAnalysisType(analysisType);
        setAnalyzingAssetId(asset.id);

        const analysisTypeLabels = {
            'nist': 'Vulnérabilités NIST',
            'mitre': 'Techniques MITRE ATT&CK'
        };

        const toastId = showLoadingToast(`Analyse ${analysisTypeLabels[analysisType]} en cours pour ${asset.name}...`);

        try {
            // Analyze only this specific asset
            const newResults = await ctiService.performCTIAnalysis([asset], analysisType);

            // Update only this asset with its results (merge with existing results)
            setBusinessAssets(assets => assets.map(a => {
                if (a.id === asset.id) {
                    const assetResult = newResults.assets?.[0];
                    const existingResults = a.ctiResults || {};

                    // Merge new results with existing ones
                    const mergedResults = {
                        vulnerabilities: [
                            ...(existingResults.vulnerabilities || []),
                            ...(assetResult?.vulnerabilities || [])
                        ],
                        attackTechniques: [
                            ...(existingResults.attackTechniques || []),
                            ...(assetResult?.attackTechniques || [])
                        ],
                        dataSource: existingResults.dataSource
                            ? `${existingResults.dataSource} + ${newResults.dataSource}`
                            : newResults.dataSource,
                        analysisDate: newResults.analysisDate
                    };

                    return {
                        ...a,
                        ctiResults: assetResult ? mergedResults : existingResults,
                        isAnalyzing: false
                    };
                }
                return { ...a, isAnalyzing: false };
            }));

            updateToast(toastId, `Analyse ${analysisTypeLabels[analysisType]} terminée pour ${asset.name} !`, 'success');
        } catch (err) {
            console.error('CTI Analysis error:', err);
            // Remove analyzing state on error
            setBusinessAssets(assets => assets.map(a =>
                a.id === asset.id ? { ...a, isAnalyzing: false } : a
            ));
            updateToast(toastId, `Erreur d'analyse: ${err.message}`, 'error');
        } finally {
            setIsLoading(s => ({ ...s, analysis: false }));
            setCurrentAnalysisType(null);
            setAnalyzingAssetId(null);
        }
    };

    // Handle saving CTI results
    const handleSave = async () => {
        const analyzedAssets = businessAssets.filter(asset => asset.ctiResults);
        if (analyzedAssets.length === 0) {
            showErrorToast("Aucun résultat à sauvegarder");
            return;
        }

        setIsSaving(true);
        const toastId = showLoadingToast("Sauvegarde des résultats CTI en cours...");

        try {
            // Prepare data for saving
            const saveData = {
                analysisId: currentAnalysis.id,
                attackPathId: selectedPath.id,
                results: {
                    assets: analyzedAssets.map(asset => ({
                        id: asset.id,
                        name: asset.name,
                        type: asset.type,
                        vendor: asset.vendor,
                        ctiResults: asset.ctiResults
                    })),
                    totalVulnerabilities: analyzedAssets.reduce((sum, asset) =>
                        sum + (asset.ctiResults?.vulnerabilities?.length || 0), 0
                    ),
                    totalAttackTechniques: analyzedAssets.reduce((sum, asset) =>
                        sum + (asset.ctiResults?.attackTechniques?.length || 0), 0
                    ),
                    analysisDate: new Date().toISOString(),
                    attackPath: {
                        id: selectedPath.id,
                        referenceCode: selectedPath.referenceCode,
                        objectifVise: selectedPath.objectifVise
                    }
                }
            };

            // Save to backend
            await api.post('/cti-results', saveData);

            updateToast(toastId, "Résultats CTI sauvegardés avec succès !", 'success');
        } catch (err) {
            console.error('Save error:', err);
            updateToast(toastId, `Erreur de sauvegarde: ${err.message}`, 'error');
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <div className="bg-gray-50 min-h-screen p-4 sm:p-6 lg:p-8">
            <div className="max-w-6xl mx-auto space-y-8">
                <header>
                    <h1 className="text-3xl font-bold text-gray-900">Intelligence des Menaces</h1>
                    <p className="text-lg text-gray-600">Analyse : <span className="font-semibold text-gray-700">{currentAnalysis.name}</span></p>
                </header>

                {error && <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md" role="alert"><p>{error}</p></div>}

                {/* Step 1: Attack Path Selection (the part you like) */}
                <Section step={1} icon={<Target size={24} />} title="Sélectionner un Chemin d'Attaque">
                    <AttackPathCarousel attackPaths={attackPaths} selectedPath={selectedPath} onSelect={setSelectedPath} isLoading={isLoading.paths} />
                </Section>

                {/* Step 2: Business Assets with CTI Analysis */}
                {selectedPath && (
                    <Section step={2} icon={<Database size={24} />} title="Biens Supports - Analyse CTI">
                        <BusinessAssetsCards
                            assets={businessAssets}
                            onAnalyze={handleAnalysis}
                            isLoading={isLoading.analysis}
                            analysisType={currentAnalysisType}
                        />
                    </Section>
                )}

                {/* Step 3: Results Summary */}
                {businessAssets.some(asset => asset.ctiResults) && (
                    <Section step={3} icon={<Zap size={24} />} title="Résultats de l'Analyse CTI">
                        <ResultsSummary assets={businessAssets} />
                    </Section>
                )}
            </div>
        </div>
    );
};

export default ThreatIntelligenceModern;
export { ThreatIntelligenceModern as ThreatIntelligence };
