// src/services/ctiService.js
// Real CTI Analysis Service using NIST NVD and MITRE ATT&CK APIs

class CTIService {
  constructor() {
    this.nistBaseUrl = 'https://services.nvd.nist.gov/rest/json/cves/2.0';
    this.mitreBaseUrl = 'https://attack.mitre.org/api/v2';
    this.mitreStixUrl = 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json';

    // Rate limiting for NIST API (max 5 requests per 30 seconds without API key)
    this.requestQueue = [];
    this.isProcessing = false;

    // Cache for MITRE data (refresh every 24 hours)
    this.mitreCache = {
      data: null,
      lastUpdated: null,
      cacheExpiry: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
    };
  }

  // Generate CPE (Common Platform Enumeration) string from asset info
  generateCPE(asset) {
    if (asset.vendor && asset.product) {
      // Format: cpe:2.3:a:vendor:product:version:*:*:*:*:*:*:*
      const vendor = asset.vendor.toLowerCase().replace(/\s+/g, '_');
      const product = asset.product.toLowerCase().replace(/\s+/g, '_');
      const version = asset.version || '*';
      return `cpe:2.3:a:${vendor}:${product}:${version}:*:*:*:*:*:*:*`;
    }
    return null;
  }

  // Search for vulnerabilities using NIST NVD API (simple keyword search)
  async searchVulnerabilities(asset) {
    try {
      console.log(`[CTI] Searching vulnerabilities for asset: ${asset.name}`);

      // Extract keywords from asset name and properties
      const keywords = this.extractKeywords(asset);
      if (!keywords || keywords.length === 0) {
        console.log(`[CTI] No keywords extracted for asset: ${asset.name}`);
        return [];
      }

      const keywordString = keywords.join(' ');
      console.log(`[CTI] Searching NIST with keywords: ${keywordString}`);

      // Use backend proxy for NIST search
      const params = new URLSearchParams({
        keywordSearch: keywordString,
        resultsPerPage: '15',
        startIndex: '0'
      });

      const response = await fetch(`${this.nistBaseUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        console.error(`[CTI] NIST API error: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      console.log(`[CTI] NIST response for ${asset.name}:`, {
        totalResults: data.totalResults || 0,
        vulnerabilitiesCount: data.vulnerabilities?.length || 0
      });

      if (data.vulnerabilities && data.vulnerabilities.length > 0) {
        return data.vulnerabilities.map(vuln => ({
          cveId: vuln.cve.id,
          description: vuln.cve.descriptions.find(d => d.lang === 'en')?.value || 'No description available',
          severity: this.extractSeverity(vuln.cve.metrics).severity,
          score: this.extractSeverity(vuln.cve.metrics).score,
          publishedDate: vuln.cve.published,
          lastModifiedDate: vuln.cve.lastModified,
          references: vuln.cve.references?.slice(0, 3) || [],
          weaknesses: this.extractWeaknesses(vuln.cve.weaknesses)
        }));
      }

      return [];
    } catch (error) {
      console.error(`[CTI] Error searching vulnerabilities for ${asset.name}:`, error);
      return [];
    }
  }

  // Extract keywords from asset for NIST search
  extractKeywords(asset) {
    const keywords = [];

    // Add vendor and product if available
    if (asset.vendor) keywords.push(asset.vendor);
    if (asset.product) keywords.push(asset.product);

    // Extract from asset name using simple patterns
    const assetName = asset.name || '';

    // Common technology keywords
    const techKeywords = [
      'Microsoft', 'Windows', 'Office', 'Exchange',
      'Oracle', 'MySQL', 'Database',
      'Cisco', 'Router', 'Switch', 'Firewall',
      'Apache', 'HTTP', 'Server', 'Tomcat',
      'VMware', 'vSphere', 'ESXi',
      'Linux', 'Ubuntu', 'CentOS', 'RedHat',
      'Java', 'PHP', 'Python', 'Node',
      'Fortinet', 'FortiGate', 'Juniper'
    ];

    // Find matching keywords in asset name
    techKeywords.forEach(keyword => {
      if (assetName.toLowerCase().includes(keyword.toLowerCase())) {
        keywords.push(keyword);
      }
    });

    // If no tech keywords found, use words from asset name
    if (keywords.length === 0) {
      const words = assetName.split(/\s+/).filter(word =>
        word.length > 2 &&
        !/^\d+$/.test(word) && // Skip pure numbers
        !['server', 'client', 'machine', 'device', 'system'].includes(word.toLowerCase())
      );
      keywords.push(...words.slice(0, 2)); // Take first 2 meaningful words
    }

    // Remove duplicates and limit to 3 keywords
    return [...new Set(keywords)].slice(0, 3);
  }

  // Extract weaknesses (CWE) from CVE data
  extractWeaknesses(weaknesses) {
    if (!weaknesses || !weaknesses.length) return [];

    return weaknesses.map(weakness => ({
      id: weakness.source === 'cwe' ? `CWE-${weakness.type}` : weakness.type,
      description: weakness.description?.[0]?.value || 'No description available'
    })).slice(0, 3); // Limit to 3 most relevant weaknesses
  }

  // Extract severity from CVSS metrics
  extractSeverity(metrics) {
    if (metrics?.cvssMetricV31 && metrics.cvssMetricV31.length > 0) {
      return {
        score: metrics.cvssMetricV31[0].cvssData.baseScore,
        severity: metrics.cvssMetricV31[0].cvssData.baseSeverity,
        vector: metrics.cvssMetricV31[0].cvssData.vectorString
      };
    }
    if (metrics?.cvssMetricV30 && metrics.cvssMetricV30.length > 0) {
      return {
        score: metrics.cvssMetricV30[0].cvssData.baseScore,
        severity: metrics.cvssMetricV30[0].cvssData.baseSeverity,
        vector: metrics.cvssMetricV30[0].cvssData.vectorString
      };
    }
    if (metrics?.cvssMetricV2 && metrics.cvssMetricV2.length > 0) {
      return {
        score: metrics.cvssMetricV2[0].cvssData.baseScore,
        severity: this.mapV2Severity(metrics.cvssMetricV2[0].cvssData.baseScore),
        vector: metrics.cvssMetricV2[0].cvssData.vectorString
      };
    }
    return { score: 0, severity: 'UNKNOWN', vector: '' };
  }

  // Map CVSS v2 score to severity
  mapV2Severity(score) {
    if (score >= 9.0) return 'CRITICAL';
    if (score >= 7.0) return 'HIGH';
    if (score >= 4.0) return 'MEDIUM';
    return 'LOW';
  }

  // Enhanced MITRE ATT&CK techniques database
  getEnhancedTechniqueDatabase() {
    const database = {
      // Reconnaissance Phase Techniques
      reconnaissance: [
        { id: 'T1595.002', name: 'Vulnerability Scanning', tactic: 'Reconnaissance', description: 'Adversaries may scan victims for vulnerabilities that can be used during targeting.' },
        { id: 'T1592.002', name: 'Software', tactic: 'Reconnaissance', description: 'Adversaries may gather information about the victim\'s host software that can be used during targeting.' },
        { id: 'T1590.005', name: 'IP Addresses', tactic: 'Reconnaissance', description: 'Adversaries may gather the victim\'s IP addresses that can be used during targeting.' },
        { id: 'T1590.002', name: 'DNS', tactic: 'Reconnaissance', description: 'Adversaries may gather information about the victim\'s DNS that can be used during targeting.' },
        { id: 'T1589.002', name: 'Email Addresses', tactic: 'Reconnaissance', description: 'Adversaries may gather email addresses that can be used during targeting.' }
      ],

      // Initial Access Techniques
      initialAccess: [
        { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program.' },
        { id: 'T1566.001', name: 'Spearphishing Attachment', tactic: 'Initial Access', description: 'Adversaries may send spearphishing emails with a malicious attachment.' },
        { id: 'T1566.002', name: 'Spearphishing Link', tactic: 'Initial Access', description: 'Adversaries may send spearphishing emails with a malicious link.' },
        { id: 'T1078.004', name: 'Cloud Accounts', tactic: 'Initial Access', description: 'Adversaries may obtain and abuse credentials of a cloud account.' },
        { id: 'T1133', name: 'External Remote Services', tactic: 'Initial Access', description: 'Adversaries may leverage external-facing remote services to initially access.' }
      ],

      // Execution Techniques
      execution: [
        { id: 'T1059.001', name: 'PowerShell', tactic: 'Execution', description: 'Adversaries may abuse PowerShell commands and scripts for execution.' },
        { id: 'T1059.003', name: 'Windows Command Shell', tactic: 'Execution', description: 'Adversaries may abuse the Windows command shell for execution.' },
        { id: 'T1059.004', name: 'Unix Shell', tactic: 'Execution', description: 'Adversaries may abuse Unix shell commands and scripts for execution.' },
        { id: 'T1203', name: 'Exploitation for Client Execution', tactic: 'Execution', description: 'Adversaries may exploit software vulnerabilities in client applications to execute code.' },
        { id: 'T1204.002', name: 'Malicious File', tactic: 'Execution', description: 'An adversary may rely upon a user opening a malicious file in order to gain execution.' }
      ],

      // Persistence Techniques
      persistence: [
        { id: 'T1053.005', name: 'Scheduled Task', tactic: 'Persistence', description: 'Adversaries may abuse the Windows Task Scheduler to perform task scheduling for initial or recurring execution.' },
        { id: 'T1547.001', name: 'Registry Run Keys / Startup Folder', tactic: 'Persistence', description: 'Adversaries may achieve persistence by adding a program to a startup folder.' },
        { id: 'T1078', name: 'Valid Accounts', tactic: 'Persistence', description: 'Adversaries may obtain and abuse credentials of existing accounts.' },
        { id: 'T1543.003', name: 'Windows Service', tactic: 'Persistence', description: 'Adversaries may create or modify Windows services to repeatedly execute malicious payloads.' }
      ],

      // Credential Access Techniques
      credentialAccess: [
        { id: 'T1552.001', name: 'Credentials In Files', tactic: 'Credential Access', description: 'Adversaries may search local file systems and remote file shares for files containing insecurely stored credentials.' },
        { id: 'T1003.001', name: 'LSASS Memory', tactic: 'Credential Access', description: 'Adversaries may attempt to access credential material stored in the process memory of the Local Security Authority Subsystem Service.' },
        { id: 'T1110.001', name: 'Password Guessing', tactic: 'Credential Access', description: 'Adversaries may use a single or small list of commonly used passwords against many different accounts.' },
        { id: 'T1558.003', name: 'Kerberoasting', tactic: 'Credential Access', description: 'Adversaries may abuse a valid Kerberos ticket-granting ticket or sniff network traffic to obtain a ticket-granting service ticket.' }
      ],

      // Discovery Techniques
      discovery: [
        { id: 'T1018', name: 'Remote System Discovery', tactic: 'Discovery', description: 'Adversaries may attempt to get a listing of other systems by IP address, hostname, or other logical identifier.' },
        { id: 'T1083', name: 'File and Directory Discovery', tactic: 'Discovery', description: 'Adversaries may enumerate files and directories or may search in specific locations of a host or network share.' },
        { id: 'T1087.001', name: 'Local Account', tactic: 'Discovery', description: 'Adversaries may attempt to get a listing of local system accounts.' },
        { id: 'T1016', name: 'System Network Configuration Discovery', tactic: 'Discovery', description: 'Adversaries may look for details about the network configuration and settings.' }
      ],

      // Lateral Movement Techniques
      lateralMovement: [
        { id: 'T1021.001', name: 'Remote Desktop Protocol', tactic: 'Lateral Movement', description: 'Adversaries may use Valid Accounts to log into a computer using the Remote Desktop Protocol.' },
        { id: 'T1021.002', name: 'SMB/Windows Admin Shares', tactic: 'Lateral Movement', description: 'Adversaries may use Valid Accounts to interact with a remote network share using Server Message Block.' },
        { id: 'T1550.002', name: 'Pass the Hash', tactic: 'Lateral Movement', description: 'Adversaries may "pass the hash" using stolen password hashes to move laterally within an environment.' },
        { id: 'T1563.002', name: 'RDP Hijacking', tactic: 'Lateral Movement', description: 'Adversaries may hijack a legitimate user\'s remote desktop session to move laterally within an environment.' }
      ],

      // Collection Techniques
      collection: [
        { id: 'T1005', name: 'Data from Local System', tactic: 'Collection', description: 'Adversaries may search local system sources, such as file systems and configuration files or local databases.' },
        { id: 'T1039', name: 'Data from Network Shared Drive', tactic: 'Collection', description: 'Adversaries may search network shares on computers they have compromised to find files of interest.' },
        { id: 'T1560.001', name: 'Archive via Utility', tactic: 'Collection', description: 'Adversaries may use utilities to compress and/or encrypt collected data prior to exfiltration.' },
        { id: 'T1113', name: 'Screen Capture', tactic: 'Collection', description: 'Adversaries may attempt to take screen captures of the desktop to gather information over the course of an operation.' }
      ],

      // Exfiltration Techniques
      exfiltration: [
        { id: 'T1041', name: 'Exfiltration Over C2 Channel', tactic: 'Exfiltration', description: 'Adversaries may steal data by exfiltrating it over an existing command and control channel.' },
        { id: 'T1567.002', name: 'Exfiltration to Cloud Storage', tactic: 'Exfiltration', description: 'Adversaries may exfiltrate data to a cloud storage service rather than over their primary command and control channel.' },
        { id: 'T1048.003', name: 'Exfiltration Over Unencrypted Non-C2 Protocol', tactic: 'Exfiltration', description: 'Adversaries may steal data by exfiltrating it over an un-encrypted network protocol other than that of the existing command and control channel.' }
      ]
    };

    // Add URLs to all techniques
    Object.keys(database).forEach(category => {
      database[category] = database[category].map(technique => ({
        ...technique,
        url: this.generateMitreUrl(technique.id)
      }));
    });

    return database;
  }

  // Enhanced technique mapping based on asset types and vulnerabilities
  async getMitreAttackTechniques(assetType, vulnerabilities = []) {
    try {
      console.log(`[CTI] Getting enhanced MITRE ATT&CK techniques for asset type: ${assetType}`);

      const techniqueDb = this.getEnhancedTechniqueDatabase();
      let selectedTechniques = [];

      // Base techniques by asset type
      const assetTypeTechniques = {
        'Serveur': [
          ...techniqueDb.reconnaissance.slice(0, 2),
          ...techniqueDb.initialAccess.slice(0, 3),
          ...techniqueDb.execution.slice(0, 2),
          ...techniqueDb.persistence.slice(0, 2),
          ...techniqueDb.credentialAccess.slice(0, 2),
          ...techniqueDb.discovery.slice(0, 2),
          ...techniqueDb.lateralMovement.slice(0, 2),
          ...techniqueDb.collection.slice(0, 2),
          ...techniqueDb.exfiltration.slice(0, 2)
        ],
        'Base de données': [
          ...techniqueDb.reconnaissance.slice(0, 1),
          ...techniqueDb.initialAccess.slice(0, 2),
          ...techniqueDb.credentialAccess,
          ...techniqueDb.discovery.slice(1, 3),
          ...techniqueDb.collection,
          ...techniqueDb.exfiltration
        ],
        'Application': [
          ...techniqueDb.reconnaissance.slice(0, 2),
          ...techniqueDb.initialAccess,
          ...techniqueDb.execution,
          ...techniqueDb.persistence.slice(0, 2),
          ...techniqueDb.collection.slice(0, 2)
        ],
        'Réseau': [
          ...techniqueDb.reconnaissance,
          ...techniqueDb.credentialAccess.slice(0, 2),
          ...techniqueDb.discovery,
          ...techniqueDb.lateralMovement,
          ...techniqueDb.collection.slice(1, 3)
        ],
        'Équipement': [
          ...techniqueDb.reconnaissance.slice(0, 1),
          ...techniqueDb.initialAccess.slice(0, 2),
          ...techniqueDb.persistence.slice(0, 1),
          ...techniqueDb.discovery.slice(0, 2)
        ]
      };

      selectedTechniques = assetTypeTechniques[assetType] || [
        ...techniqueDb.reconnaissance.slice(0, 1),
        ...techniqueDb.initialAccess.slice(0, 2),
        ...techniqueDb.execution.slice(0, 1),
        ...techniqueDb.collection.slice(0, 1)
      ];

      // Enhance techniques based on vulnerabilities
      if (vulnerabilities && vulnerabilities.length > 0) {
        const vulnBasedTechniques = this.mapVulnerabilitiesToTechniques(vulnerabilities, techniqueDb);
        selectedTechniques = [...selectedTechniques, ...vulnBasedTechniques];
      }

      // Remove duplicates and limit to reasonable number
      const uniqueTechniques = selectedTechniques.filter((tech, index, self) =>
        index === self.findIndex(t => t.id === tech.id)
      ).slice(0, 15);

      console.log(`[CTI] Selected ${uniqueTechniques.length} techniques for ${assetType}`);
      return uniqueTechniques;

    } catch (error) {
      console.error(`[CTI] Error getting MITRE techniques:`, error);
      return [];
    }
  }

  // Map vulnerabilities to specific techniques
  mapVulnerabilitiesToTechniques(vulnerabilities, techniqueDb) {
    const additionalTechniques = [];

    vulnerabilities.forEach(vuln => {
      const description = vuln.description?.toLowerCase() || '';
      const severity = vuln.severity?.severity || '';

      // SQL Injection vulnerabilities
      if (description.includes('sql injection') || description.includes('sqli')) {
        additionalTechniques.push(
          { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'SQL injection exploitation' },
          { id: 'T1005', name: 'Data from Local System', tactic: 'Collection', description: 'Database data extraction via SQL injection' }
        );
      }

      // Remote Code Execution vulnerabilities
      if (description.includes('remote code execution') || description.includes('rce')) {
        additionalTechniques.push(
          { id: 'T1190', name: 'Exploit Public-Facing Application', tactic: 'Initial Access', description: 'Remote code execution exploitation' },
          { id: 'T1059.003', name: 'Windows Command Shell', tactic: 'Execution', description: 'Command execution via RCE' }
        );
      }

      // Authentication bypass vulnerabilities
      if (description.includes('authentication bypass') || description.includes('auth bypass')) {
        additionalTechniques.push(
          { id: 'T1078', name: 'Valid Accounts', tactic: 'Initial Access', description: 'Authentication bypass exploitation' }
        );
      }

      // Directory traversal vulnerabilities
      if (description.includes('directory traversal') || description.includes('path traversal')) {
        additionalTechniques.push(
          { id: 'T1083', name: 'File and Directory Discovery', tactic: 'Discovery', description: 'Directory traversal exploitation' },
          { id: 'T1552.001', name: 'Credentials In Files', tactic: 'Credential Access', description: 'Credential file access via path traversal' }
        );
      }

      // High/Critical severity vulnerabilities get additional techniques
      if (severity === 'HIGH' || severity === 'CRITICAL') {
        additionalTechniques.push(
          ...techniqueDb.persistence.slice(0, 1),
          ...techniqueDb.lateralMovement.slice(0, 1)
        );
      }
    });

    return additionalTechniques;
  }

  // Main CTI analysis function
  async performCTIAnalysis(selectedAssets, analysisType = 'combined') {
    console.log(`[CTI] Starting ${analysisType} CTI analysis for assets:`, selectedAssets);

    const dataSourceMap = {
      'nist': 'NIST NVD',
      'mitre': 'MITRE ATT&CK',
      'atlas': 'MITRE ATLAS',
      'combined': 'NIST NVD + MITRE ATT&CK'
    };

    const results = {
      totalVulnerabilities: 0,
      totalAttackTechniques: 0,
      overallRiskScore: 0,
      assets: [],
      analysisDate: new Date().toISOString(),
      dataSource: dataSourceMap[analysisType] || 'Combined Sources',
      analysisType: analysisType
    };

    try {
      // Process each asset
      for (const asset of selectedAssets) {
        console.log(`[CTI] Analyzing asset: ${asset.name} with ${analysisType} analysis`);

        let vulnerabilities = [];
        let attackTechniques = [];

        // Perform analysis based on type
        if (analysisType === 'nist' || analysisType === 'combined') {
          // Get vulnerabilities from NIST
          vulnerabilities = await this.searchVulnerabilities(asset);
        }

        if (analysisType === 'mitre' || analysisType === 'combined' || analysisType === 'comprehensive') {
          // Get live attack techniques from MITRE (based on full asset context and vulnerabilities)
          attackTechniques = await this.getLiveMitreAttackTechniques(asset, vulnerabilities);
        }

        if (analysisType === 'atlas') {
          // Get MITRE ATLAS AI/ML threats
          attackTechniques = await this.getAtlasThreats(asset);
        }

        // Calculate risk score for this asset
        const assetRiskScore = this.calculateAssetRiskScore(vulnerabilities, attackTechniques, asset.criticality);

        const assetResult = {
          ...asset,
          vulnerabilities: vulnerabilities,
          vulnerabilityCount: vulnerabilities.length,
          attackTechniques: attackTechniques,
          techniqueCount: attackTechniques.length,
          riskScore: assetRiskScore,
          highSeverityVulns: vulnerabilities.filter(v =>
            v.severity.severity === 'HIGH' || v.severity.severity === 'CRITICAL'
          ).length
        };

        results.assets.push(assetResult);
        results.totalVulnerabilities += vulnerabilities.length;
        results.totalAttackTechniques += attackTechniques.length;

        // Add delay to respect NIST API rate limits (only when calling NIST API)
        if (analysisType === 'nist' || analysisType === 'combined') {
          await this.delay(6000); // 6 seconds between requests for NIST
        } else {
          // Shorter delay for other analysis types
          await this.delay(1000); // 1 second between requests
        }
      }

      // Calculate overall risk score
      results.overallRiskScore = this.calculateOverallRiskScore(results.assets);

      console.log('[CTI] CTI analysis completed:', results);
      return results;

    } catch (error) {
      console.error('[CTI] Error during CTI analysis:', error);
      throw error;
    }
  }

  // Calculate risk score for individual asset
  calculateAssetRiskScore(vulnerabilities, attackTechniques, criticality) {
    let score = 0;

    // Base score from vulnerabilities
    vulnerabilities.forEach(vuln => {
      score += vuln.severity.score || 0;
    });

    // Add points for attack techniques
    score += attackTechniques.length * 0.5;

    // Multiply by criticality factor
    const criticalityMultiplier = {
      'critique': 1.5,
      'élevé': 1.3,
      'moyen': 1.0,
      'faible': 0.7
    };

    score *= criticalityMultiplier[criticality] || 1.0;

    // Normalize to 0-10 scale
    return Math.min(Math.round(score * 10) / 10, 10);
  }

  // Calculate overall risk score
  calculateOverallRiskScore(assets) {
    if (assets.length === 0) return 0;

    const totalScore = assets.reduce((sum, asset) => sum + asset.riskScore, 0);
    return Math.round((totalScore / assets.length) * 10) / 10;
  }

  // Fetch live MITRE ATT&CK data from GitHub repository
  async fetchLiveMitreData() {
    try {
      console.log('[CTI] Fetching live MITRE ATT&CK data...');

      // Check cache first
      const now = Date.now();
      if (this.mitreCache.data && this.mitreCache.lastUpdated &&
          (now - this.mitreCache.lastUpdated) < this.mitreCache.cacheExpiry) {
        console.log('[CTI] Using cached MITRE data');
        return this.mitreCache.data;
      }

      const response = await fetch(this.mitreStixUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
        }
      });

      if (!response.ok) {
        console.error(`[CTI] MITRE API error: ${response.status} ${response.statusText}`);
        return this.getFallbackMitreData();
      }

      const stixData = await response.json();
      console.log('[CTI] Successfully fetched MITRE STIX data');
      console.log('[CTI] MITRE STIX response structure:', {
        objectsCount: stixData.objects?.length || 0,
        firstObject: stixData.objects?.[0] || null,
        objectTypes: [...new Set(stixData.objects?.map(obj => obj.type) || [])]
      });

      // Parse STIX data to extract techniques
      const techniques = this.parseMitreStixData(stixData);

      // Update cache
      this.mitreCache.data = techniques;
      this.mitreCache.lastUpdated = now;

      console.log(`[CTI] Parsed ${techniques.length} techniques from live MITRE data`);
      return techniques;

    } catch (error) {
      console.error('[CTI] Error fetching live MITRE data:', error);
      return this.getFallbackMitreData();
    }
  }

  // Parse MITRE STIX data to extract techniques
  parseMitreStixData(stixData) {
    const techniques = [];

    if (!stixData.objects) {
      console.warn('[CTI] No objects found in MITRE STIX data');
      return this.getFallbackMitreData();
    }

    stixData.objects.forEach((obj, index) => {
      if (obj.type === 'attack-pattern' && !obj.revoked && !obj.x_mitre_deprecated) {
        // Log first few technique objects for structure analysis
        if (index < 3) {
          console.log(`[CTI] Raw MITRE technique object ${index + 1}:`, obj);
        }

        // Extract technique information
        const technique = {
          id: obj.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id || 'Unknown',
          name: obj.name || 'Unknown Technique',
          description: obj.description || 'No description available',
          tactic: obj.kill_chain_phases?.map(phase => phase.phase_name).join(', ') || 'Unknown',
          platforms: obj.x_mitre_platforms || [],
          dataSource: obj.x_mitre_data_sources || [],
          version: obj.x_mitre_version || '1.0',
          lastModified: obj.modified || obj.created,
          url: this.generateMitreUrl(obj.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id),
          // Enhanced fields from STIX data
          created: obj.created,
          modified: obj.modified,
          type: obj.type,
          spec_version: obj.spec_version,
          external_references: obj.external_references || [],
          kill_chain_phases: obj.kill_chain_phases || [],
          x_mitre_detection: obj.x_mitre_detection,
          x_mitre_is_subtechnique: obj.x_mitre_is_subtechnique,
          x_mitre_permissions_required: obj.x_mitre_permissions_required || [],
          x_mitre_effective_permissions: obj.x_mitre_effective_permissions || [],
          x_mitre_system_requirements: obj.x_mitre_system_requirements || [],
          x_mitre_impact_type: obj.x_mitre_impact_type || [],
          x_mitre_defense_bypassed: obj.x_mitre_defense_bypassed || [],
          x_mitre_remote_support: obj.x_mitre_remote_support,
          x_mitre_network_requirements: obj.x_mitre_network_requirements
        };

        // Log first processed technique for structure analysis
        if (index < 3) {
          console.log(`[CTI] Processed MITRE technique ${index + 1}:`, technique);
        }

        // Only include techniques with valid IDs
        if (technique.id.startsWith('T')) {
          techniques.push(technique);
        }
      }
    });

    return techniques;
  }

  // Fallback to enhanced static data if live fetch fails
  getFallbackMitreData() {
    console.log('[CTI] Using fallback enhanced MITRE data');
    const techniqueDb = this.getEnhancedTechniqueDatabase();

    return [
      ...techniqueDb.reconnaissance,
      ...techniqueDb.initialAccess,
      ...techniqueDb.execution,
      ...techniqueDb.persistence,
      ...techniqueDb.credentialAccess,
      ...techniqueDb.discovery,
      ...techniqueDb.lateralMovement,
      ...techniqueDb.collection,
      ...techniqueDb.exfiltration
    ];
  }

  // Enhanced technique selection using live MITRE data with multi-strategy approach
  async getLiveMitreAttackTechniques(asset, vulnerabilities = []) {
    try {
      console.log(`[CTI] Getting live MITRE ATT&CK techniques for asset:`, asset);

      // Fetch live MITRE data
      const allTechniques = await this.fetchLiveMitreData();

      // Try multiple technique selection strategies
      let relevantTechniques = [];

      // Strategy 1: Asset-specific filtering (vendor + product + type)
      console.log(`[CTI] Strategy 1: Asset-specific technique filtering for ${asset.name}`);
      relevantTechniques = await this.filterTechniquesByAsset(allTechniques, asset);

      // Strategy 2: If few results, try platform-based filtering
      if (relevantTechniques.length < 5) {
        console.log(`[CTI] Found ${relevantTechniques.length} asset-specific techniques, trying platform-based filtering`);
        const platformTechniques = this.filterTechniquesByPlatform(allTechniques, asset);
        const existingIds = new Set(relevantTechniques.map(t => t.id));
        const newTechniques = platformTechniques.filter(t => !existingIds.has(t.id));
        relevantTechniques = [...relevantTechniques, ...newTechniques];
      }

      // Strategy 3: If still few results, try tactic-based selection
      if (relevantTechniques.length < 8) {
        console.log(`[CTI] Found ${relevantTechniques.length} total techniques, adding tactic-based selection`);
        const tacticTechniques = this.selectTechniquesByTactics(allTechniques, asset);
        const existingIds = new Set(relevantTechniques.map(t => t.id));
        const newTechniques = tacticTechniques.filter(t => !existingIds.has(t.id));
        relevantTechniques = [...relevantTechniques, ...newTechniques];
      }

      // Add vulnerability-specific techniques
      const vulnTechniques = this.mapVulnerabilitiesToLiveTechniques(vulnerabilities, allTechniques);
      const existingIds = new Set(relevantTechniques.map(t => t.id));
      const newVulnTechniques = vulnTechniques.filter(t => !existingIds.has(t.id));

      // Combine all techniques
      const combinedTechniques = [...relevantTechniques, ...newVulnTechniques];
      const uniqueTechniques = combinedTechniques.slice(0, 20); // Limit to 20 most relevant

      console.log(`[CTI] MITRE technique selection summary for ${asset.name}:`, {
        assetSpecific: relevantTechniques.length - (vulnTechniques.length > 0 ? vulnTechniques.length : 0),
        vulnerabilityBased: newVulnTechniques.length,
        totalSelected: uniqueTechniques.length,
        assetType: asset.type,
        assetProduct: asset.product,
        assetVendor: asset.vendor
      });

      console.log(`[CTI] Selected techniques:`, uniqueTechniques.map(t => ({ id: t.id, name: t.name, tactic: t.tactic })));
      return uniqueTechniques;

    } catch (error) {
      console.error(`[CTI] Error getting live MITRE techniques:`, error);
      // Fallback to enhanced static techniques
      return this.getMitreAttackTechniques(asset.type, vulnerabilities);
    }
  }

  // Enhanced asset-specific technique filtering (Strategy 1)
  filterTechniquesByAsset(allTechniques, asset) {
    console.log(`[CTI] Filtering techniques for asset:`, {
      name: asset.name,
      type: asset.type,
      product: asset.product,
      vendor: asset.vendor
    });

    // Build comprehensive keyword list based on asset characteristics
    const assetKeywords = [];

    // Add keywords based on asset type
    const typeKeywords = {
      'equipements': ['system', 'endpoint', 'persistence', 'execution', 'privilege', 'hardware'],
      'logiciels': ['application', 'software', 'execution', 'exploit', 'code', 'process'],
      'reseaux': ['network', 'remote', 'lateral', 'discovery', 'sniff', 'protocol'],
      'organisation': ['social', 'phishing', 'credential', 'initial', 'user'],
      'locaux': ['physical', 'access', 'persistence', 'local']
    };
    assetKeywords.push(...(typeKeywords[asset.type] || ['system', 'network']));

    // Add vendor-specific keywords
    if (asset.vendor) {
      const vendorLower = asset.vendor.toLowerCase();
      if (vendorLower.includes('cisco')) {
        assetKeywords.push('network', 'router', 'switch', 'infrastructure', 'device', 'configuration');
      }
      if (vendorLower.includes('microsoft')) {
        assetKeywords.push('windows', 'credential', 'privilege', 'persistence', 'registry', 'service');
      }
      if (vendorLower.includes('oracle') || vendorLower.includes('mysql')) {
        assetKeywords.push('database', 'sql', 'data', 'credential', 'file', 'query');
      }
      if (vendorLower.includes('apache')) {
        assetKeywords.push('web', 'server', 'application', 'remote', 'http');
      }
    }

    // Add product-specific keywords
    if (asset.product) {
      const productLower = asset.product.toLowerCase();
      if (productLower.includes('router') || productLower.includes('isr')) {
        assetKeywords.push('network', 'remote', 'lateral', 'discovery', 'configuration', 'device');
      }
      if (productLower.includes('database') || productLower.includes('mysql') || productLower.includes('sql')) {
        assetKeywords.push('database', 'sql', 'data', 'credential', 'file', 'injection');
      }
      if (productLower.includes('server') || productLower.includes('apache') || productLower.includes('nginx')) {
        assetKeywords.push('web', 'server', 'application', 'remote', 'exploit');
      }
      if (productLower.includes('windows')) {
        assetKeywords.push('windows', 'credential', 'privilege', 'persistence', 'execution');
      }
    }

    // Remove duplicates
    const uniqueKeywords = [...new Set(assetKeywords)];
    console.log(`[CTI] Using keywords for technique filtering:`, uniqueKeywords);

    const filteredTechniques = allTechniques.filter(tech => {
      const searchText = `${tech.name} ${tech.description} ${tech.tactic} ${tech.platforms?.join(' ') || ''}`.toLowerCase();
      return uniqueKeywords.some(keyword => searchText.includes(keyword));
    }).slice(0, 12);

    console.log(`[CTI] Asset-specific filtering: ${filteredTechniques.length} techniques from ${allTechniques.length} total`);
    return filteredTechniques;
  }

  // Platform-based technique filtering (Strategy 2)
  filterTechniquesByPlatform(allTechniques, asset) {
    console.log(`[CTI] Platform-based filtering for ${asset.name}`);

    // Determine relevant platforms based on asset characteristics
    const relevantPlatforms = [];

    if (asset.vendor?.toLowerCase().includes('cisco') || asset.product?.toLowerCase().includes('router')) {
      relevantPlatforms.push('network', 'linux'); // Most network devices run Linux-based OS
    }
    if (asset.vendor?.toLowerCase().includes('microsoft') || asset.product?.toLowerCase().includes('windows')) {
      relevantPlatforms.push('windows');
    }
    if (asset.product?.toLowerCase().includes('linux') || asset.product?.toLowerCase().includes('unix')) {
      relevantPlatforms.push('linux');
    }
    if (asset.type === 'reseaux') {
      relevantPlatforms.push('network');
    }

    // Default platforms if none specified
    if (relevantPlatforms.length === 0) {
      relevantPlatforms.push('linux', 'windows', 'network');
    }

    const platformTechniques = allTechniques.filter(tech => {
      if (!tech.platforms || tech.platforms.length === 0) return true; // Include techniques without platform restrictions

      return tech.platforms.some(platform => {
        const platformLower = platform.toLowerCase();
        return relevantPlatforms.some(relevantPlatform =>
          platformLower.includes(relevantPlatform) || relevantPlatform.includes(platformLower)
        );
      });
    }).slice(0, 8);

    console.log(`[CTI] Platform-based filtering: ${platformTechniques.length} techniques for platforms:`, relevantPlatforms);
    return platformTechniques;
  }

  // Tactic-based technique selection (Strategy 3)
  selectTechniquesByTactics(allTechniques, asset) {
    console.log(`[CTI] Tactic-based selection for ${asset.name}`);

    // Define relevant tactics based on asset type
    const relevantTactics = [];

    if (asset.type === 'reseaux' || asset.product?.toLowerCase().includes('router')) {
      relevantTactics.push('discovery', 'lateral-movement', 'collection', 'persistence');
    } else if (asset.type === 'equipements' && asset.product?.toLowerCase().includes('database')) {
      relevantTactics.push('credential-access', 'collection', 'exfiltration', 'persistence');
    } else if (asset.type === 'logiciels' || asset.product?.toLowerCase().includes('server')) {
      relevantTactics.push('initial-access', 'execution', 'persistence', 'privilege-escalation');
    } else {
      // Default tactics for unknown asset types
      relevantTactics.push('initial-access', 'execution', 'persistence', 'discovery');
    }

    const tacticTechniques = [];
    relevantTactics.forEach(tactic => {
      const tacticMatches = allTechniques.filter(tech =>
        tech.tactic?.toLowerCase().includes(tactic) ||
        tech.tactic?.toLowerCase().replace(/\s+/g, '-').includes(tactic)
      ).slice(0, 2); // 2 techniques per tactic
      tacticTechniques.push(...tacticMatches);
    });

    console.log(`[CTI] Tactic-based selection: ${tacticTechniques.length} techniques for tactics:`, relevantTactics);
    return tacticTechniques;
  }

  // Filter techniques by asset type using live data
  filterTechniquesByAssetType(allTechniques, assetType) {
    const assetTypeKeywords = {
      'Serveur': ['server', 'web', 'application', 'service', 'network', 'remote'],
      'Base de données': ['database', 'sql', 'data', 'credential', 'file'],
      'Application': ['application', 'web', 'client', 'execution', 'exploit'],
      'Réseau': ['network', 'remote', 'lateral', 'discovery', 'sniff'],
      'Équipement': ['system', 'endpoint', 'persistence', 'execution']
    };

    const keywords = assetTypeKeywords[assetType] || ['system', 'network'];

    return allTechniques.filter(tech => {
      const searchText = `${tech.name} ${tech.description} ${tech.tactic}`.toLowerCase();
      return keywords.some(keyword => searchText.includes(keyword));
    }).slice(0, 15);
  }

  // Map vulnerabilities to live techniques
  mapVulnerabilitiesToLiveTechniques(vulnerabilities, allTechniques) {
    const additionalTechniques = [];

    vulnerabilities.forEach(vuln => {
      const description = vuln.description?.toLowerCase() || '';

      // Find relevant techniques based on vulnerability type
      if (description.includes('sql injection')) {
        const sqlTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('exploit') ||
          tech.name.toLowerCase().includes('injection') ||
          tech.description.toLowerCase().includes('database')
        );
        additionalTechniques.push(...sqlTechniques.slice(0, 2));
      }

      if (description.includes('remote code execution')) {
        const rceTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('exploit') ||
          tech.name.toLowerCase().includes('execution') ||
          tech.tactic.toLowerCase().includes('execution')
        );
        additionalTechniques.push(...rceTechniques.slice(0, 2));
      }

      if (description.includes('authentication')) {
        const authTechniques = allTechniques.filter(tech =>
          tech.name.toLowerCase().includes('account') ||
          tech.name.toLowerCase().includes('credential') ||
          tech.tactic.toLowerCase().includes('credential')
        );
        additionalTechniques.push(...authTechniques.slice(0, 2));
      }
    });

    return additionalTechniques;
  }

  // Generate MITRE ATT&CK URL - always use main technique (remove sub-technique part)
  generateMitreUrl(techniqueId) {
    if (!techniqueId || !techniqueId.startsWith('T')) {
      return 'https://attack.mitre.org/techniques/';
    }

    // For sub-techniques (e.g., T1011.001), use only the main technique part (T1011)
    const mainTechniqueId = techniqueId.includes('.') ? techniqueId.split('.')[0] : techniqueId;

    return `https://attack.mitre.org/techniques/${mainTechniqueId}/`;
  }

  // Utility function for delays
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Clear MITRE cache (useful for testing)
  clearMitreCache() {
    console.log('[CTI] Clearing MITRE cache');
    this.mitreCache.data = null;
    this.mitreCache.lastUpdated = null;
  }

  // Clear EUVD cache (useful for testing)
  clearEUVDCache() {
    console.log('[CTI] Clearing EUVD cache');
    this.euvdCache.criticalVulns.data = null;
    this.euvdCache.criticalVulns.lastUpdated = null;
    this.euvdCache.exploitedVulns.data = null;
    this.euvdCache.exploitedVulns.lastUpdated = null;
  }

  // Clear all caches
  clearAllCaches() {
    console.log('[CTI] Clearing all caches');
    this.clearMitreCache();
    this.clearEUVDCache();
  }
}

export default new CTIService();
